package service

import (
	"encoding/json"
	"fmt"
	"io"
	"os"
	"pantheon-backend/model"
	"pantheon-backend/provider"
	"path/filepath"
	"strings"
	"time"

	"github.com/google/uuid"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type SnapService struct {
	db       *gorm.DB
	provider provider.Provider
	dataDir  string
}

type ExecRequest struct {
	Command     []string          `json:"command"`
	WorkingDir  string            `json:"workingDir,omitempty"`
	NewSnapName string            `json:"newSnapName,omitempty"`
	Envs        map[string]string `json:"envs,omitempty"`
}

type ExecResponse struct {
	NewSnapID string `json:"newSnapID"`
}

type SnapInfo struct {
	Name         string    `json:"name"`
	SourceSnapID string    `json:"sourceSnapID"`
	BaseSnapID   string    `json:"baseSnapID"`
	StartAt      time.Time `json:"startAt"`
	FinishAt     time.Time `json:"finishAt"`
	IsRunning    bool      `json:"isRunning"`
	IsShaked     bool      `json:"isShaked"`
}

type TreeNode struct {
	SnapID   string      `json:"snapID"`
	Name     string      `json:"name"`
	Children []*TreeNode `json:"children,omitempty"`
}

type ImportRequest struct {
	ID          string            `json:"id"`
	Envs        map[string]string `json:"envs"`
	DockerImage string            `json:"dockerImage"`
}

func NewSnapService(db *gorm.DB, provider provider.Provider, dataDir string) *SnapService {
	return &SnapService{
		db:       db,
		provider: provider,
		dataDir:  dataDir,
	}
}

func (s *SnapService) ImportSeedSnap(req *ImportRequest) error {
	// Validate ID format
	if strings.Contains(req.ID, "/") {
		return fmt.Errorf("seed snap ID must not contain '/'")
	}

	// Check if already exists
	var existing model.Snap
	if err := s.db.Where("id = ?", req.ID).First(&existing).Error; err == nil {
		return fmt.Errorf("seed snap with ID %s already exists", req.ID)
	}

	// Import via provider
	providerID, err := s.provider.Import(req.DockerImage)
	if err != nil {
		return fmt.Errorf("failed to import image: %w", err)
	}

	// Create ancestry path
	ancestryPath, _ := json.Marshal([]string{req.ID})

	// Create snap record
	snap := model.Snap{
		ID:           req.ID,
		ForestID:     "", // Seed snaps are not bound to any forest
		Name:         req.ID,
		ProviderID:   providerID,
		SourceSnapID: "",
		BaseSnapID:   req.ID,
		AncestryPath: datatypes.JSON(ancestryPath),
		Command:      "",
		WorkDir:      "",
		Envs:         datatypes.JSON("{}"),
		Status:       "FINISHED",
		StartedAt:    nil,
		FinishedAt:   nil,
		OutputPath:   "",
	}

	if len(req.Envs) > 0 {
		envsJSON, _ := json.Marshal(req.Envs)
		snap.Envs = datatypes.JSON(envsJSON)
	}

	return s.db.Create(&snap).Error
}

func (s *SnapService) ExecSnap(forestID, snapID string, req *ExecRequest) (*ExecResponse, error) {
	// Find source snap
	var sourceSnap model.Snap
	if err := s.db.Where("id = ?", snapID).First(&sourceSnap).Error; err != nil {
		return nil, fmt.Errorf("source snap not found: %w", err)
	}

	// Generate new snap ID
	newSnapID := s.generateSnapID(sourceSnap.BaseSnapID)

	// Parse envs from source snap
	var sourceEnvs map[string]string
	if err := json.Unmarshal(sourceSnap.Envs, &sourceEnvs); err != nil {
		sourceEnvs = make(map[string]string)
	}

	// Merge with request envs
	finalEnvs := make(map[string]string)
	for k, v := range sourceEnvs {
		finalEnvs[k] = v
	}
	for k, v := range req.Envs {
		finalEnvs[k] = v
	}

	// Create new snap record (PENDING)
	now := time.Now()
	var ancestryPath []string
	json.Unmarshal(sourceSnap.AncestryPath, &ancestryPath)
	ancestryPath = append(ancestryPath, newSnapID)
	newAncestryJSON, _ := json.Marshal(ancestryPath)

	envsJSON, _ := json.Marshal(finalEnvs)
	commandJSON, _ := json.Marshal(req.Command)

	newSnap := model.Snap{
		ID:           newSnapID,
		ForestID:     forestID,
		Name:         req.NewSnapName,
		ProviderID:   "", // Will be set after execution
		SourceSnapID: snapID,
		BaseSnapID:   sourceSnap.BaseSnapID,
		AncestryPath: datatypes.JSON(newAncestryJSON),
		Command:      string(commandJSON),
		WorkDir:      req.WorkingDir,
		Envs:         datatypes.JSON(envsJSON),
		Status:       "PENDING",
		StartedAt:    &now,
		FinishedAt:   nil,
		OutputPath:   fmt.Sprintf("%s/%s/output.log", forestID, newSnapID),
	}

	if err := s.db.Create(&newSnap).Error; err != nil {
		return nil, fmt.Errorf("failed to create snap record: %w", err)
	}

	// Update status to RUNNING
	s.db.Model(&newSnap).Update("status", "RUNNING")

	// Execute via provider
	output, err := s.provider.Exec(sourceSnap.ProviderID, req.Command, req.WorkingDir, finalEnvs)
	if err != nil {
		s.db.Model(&newSnap).Updates(map[string]interface{}{
			"status":      "FAILED",
			"finished_at": time.Now(),
		})
		return nil, fmt.Errorf("execution failed: %w", err)
	}

	// Save output to file
	if err := s.saveOutput(newSnap.OutputPath, output.Stdout, output.Stderr); err != nil {
		s.db.Model(&newSnap).Updates(map[string]interface{}{
			"status":      "FAILED",
			"finished_at": time.Now(),
		})
		return nil, fmt.Errorf("failed to save output: %w", err)
	}

	// Update snap with success
	finishedAt := time.Now()
	s.db.Model(&newSnap).Updates(map[string]interface{}{
		"provider_id": output.NewProviderID,
		"status":      "FINISHED",
		"finished_at": finishedAt,
	})

	return &ExecResponse{NewSnapID: newSnapID}, nil
}

func (s *SnapService) GetSnapInfo(snapID string) (*SnapInfo, error) {
	var snap model.Snap
	if err := s.db.Where("id = ?", snapID).First(&snap).Error; err != nil {
		return nil, fmt.Errorf("snap not found: %w", err)
	}

	info := &SnapInfo{
		Name:         snap.Name,
		SourceSnapID: snap.SourceSnapID,
		BaseSnapID:   snap.BaseSnapID,
		IsRunning:    snap.Status == "RUNNING",
		IsShaked:     false, // TODO: implement tree shake logic
	}

	if snap.StartedAt != nil {
		info.StartAt = *snap.StartedAt
	}
	if snap.FinishedAt != nil {
		info.FinishAt = *snap.FinishedAt
	}

	return info, nil
}

func (s *SnapService) GetSnapOutput(forestID, snapID string) (io.ReadCloser, error) {
	var snap model.Snap
	if err := s.db.Where("id = ? AND forest_id = ?", snapID, forestID).First(&snap).Error; err != nil {
		return nil, fmt.Errorf("snap not found: %w", err)
	}

	if snap.OutputPath == "" {
		return nil, fmt.Errorf("no output available for snap")
	}

	outputFile := filepath.Join(s.dataDir, snap.OutputPath)
	file, err := os.Open(outputFile)
	if err != nil {
		return nil, fmt.Errorf("failed to open output file: %w", err)
	}

	return file, nil
}

func (s *SnapService) GetSnapTree(snapID string, maxDepth int) (*TreeNode, error) {
	// Get all descendants using ancestry path
	var snaps []model.Snap

	// Use LIKE query for SQLite compatibility
	if err := s.db.Where("ancestry_path LIKE ?", fmt.Sprintf(`%%"%s"%%`, snapID)).Find(&snaps).Error; err != nil {
		return nil, fmt.Errorf("failed to query descendants: %w", err)
	}

	// Build tree structure
	return s.buildTree(snaps, snapID, maxDepth, 0)
}

func (s *SnapService) GetSnapLeaves(snapID string) ([]string, error) {
	// Get all descendants
	var descendants []model.Snap
	if err := s.db.Where("ancestry_path LIKE ?", fmt.Sprintf(`%%"%s"%%`, snapID)).Find(&descendants).Error; err != nil {
		return nil, fmt.Errorf("failed to query descendants: %w", err)
	}

	// Find leaves (snaps that are not source of any other snap)
	var leaves []string
	descendantIDs := make(map[string]bool)
	for _, snap := range descendants {
		descendantIDs[snap.ID] = true
	}

	for _, snap := range descendants {
		isLeaf := true
		for _, other := range descendants {
			if other.SourceSnapID == snap.ID {
				isLeaf = false
				break
			}
		}
		if isLeaf {
			leaves = append(leaves, snap.ID)
		}
	}

	return leaves, nil
}

func (s *SnapService) BurnForest(forestID string) error {
	// Get all snaps in forest
	var snaps []model.Snap
	if err := s.db.Where("forest_id = ?", forestID).Find(&snaps).Error; err != nil {
		return fmt.Errorf("failed to query forest snaps: %w", err)
	}

	// Clean up provider resources
	for _, snap := range snaps {
		if snap.ProviderID != "" {
			s.provider.Cleanup(snap.ProviderID)
		}
	}

	// Delete output files
	forestDir := filepath.Join(s.dataDir, forestID)
	os.RemoveAll(forestDir)

	// Delete database records
	return s.db.Where("forest_id = ?", forestID).Delete(&model.Snap{}).Error
}

func (s *SnapService) generateSnapID(baseSnapID string) string {
	shortID := uuid.New().String()[:8]
	return fmt.Sprintf("%s/%s", baseSnapID, shortID)
}

func (s *SnapService) saveOutput(outputPath string, stdout, stderr io.ReadCloser) error {
	fullPath := filepath.Join(s.dataDir, outputPath)
	if err := os.MkdirAll(filepath.Dir(fullPath), 0755); err != nil {
		return err
	}

	file, err := os.Create(fullPath)
	if err != nil {
		return err
	}
	defer file.Close()

	// Write stdout
	if stdout != nil {
		io.Copy(file, stdout)
		stdout.Close()
	}

	// Write stderr (append)
	if stderr != nil {
		file.WriteString("\n--- STDERR ---\n")
		io.Copy(file, stderr)
		stderr.Close()
	}

	return nil
}

func (s *SnapService) buildTree(snaps []model.Snap, rootID string, maxDepth, currentDepth int) (*TreeNode, error) {
	if maxDepth > 0 && currentDepth >= maxDepth {
		return nil, nil
	}

	// Find root snap
	var rootSnap *model.Snap
	for _, snap := range snaps {
		if snap.ID == rootID {
			rootSnap = &snap
			break
		}
	}

	if rootSnap == nil {
		return nil, fmt.Errorf("root snap not found")
	}

	node := &TreeNode{
		SnapID: rootSnap.ID,
		Name:   rootSnap.Name,
	}

	// Find children
	for _, snap := range snaps {
		if snap.SourceSnapID == rootID {
			child, err := s.buildTree(snaps, snap.ID, maxDepth, currentDepth+1)
			if err != nil {
				return nil, err
			}
			if child != nil {
				node.Children = append(node.Children, child)
			}
		}
	}

	return node, nil
}
