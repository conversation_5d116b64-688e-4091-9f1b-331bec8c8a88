package test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"os"
	"pantheon-backend/api"
	"pantheon-backend/model"
	"pantheon-backend/provider/local"
	"pantheon-backend/service"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/wait"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

func TestPantheonE2E(t *testing.T) {
	// Start MySQL container
	ctx := context.Background()

	// Set Docker host for testcontainers if needed
	if os.Getenv("DOCKER_HOST") == "" {
		os.Setenv("DOCKER_HOST", "unix:///var/run/docker.sock")
	}

	mysqlContainer, err := testcontainers.GenericContainer(ctx, testcontainers.GenericContainerRequest{
		ContainerRequest: testcontainers.ContainerRequest{
			Image:        "mysql:8.0",
			ExposedPorts: []string{"3306/tcp"},
			Env: map[string]string{
				"MYSQL_ROOT_PASSWORD": "testpassword",
				"MYSQL_DATABASE":      "pantheon_test",
			},
			WaitingFor: wait.ForLog("ready for connections").WithOccurrence(2).WithStartupTimeout(60 * time.Second),
		},
		Started: true,
	})
	require.NoError(t, err)
	defer mysqlContainer.Terminate(ctx)

	// Get MySQL connection details
	host, err := mysqlContainer.Host(ctx)
	require.NoError(t, err)
	port, err := mysqlContainer.MappedPort(ctx, "3306")
	require.NoError(t, err)

	// Wait a bit for MySQL to be fully ready
	time.Sleep(5 * time.Second)

	// Connect to database
	dsn := fmt.Sprintf("root:testpassword@tcp(%s:%s)/pantheon_test?charset=utf8mb4&parseTime=True&loc=Local", host, port.Port())
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	require.NoError(t, err)

	// Auto migrate
	err = db.AutoMigrate(&model.Snap{})
	require.NoError(t, err)

	// Create temp data directory
	dataDir, err := os.MkdirTemp("", "pantheon-test-*")
	require.NoError(t, err)
	defer os.RemoveAll(dataDir)

	// Initialize provider
	provider, err := local.NewLocalProvider()
	require.NoError(t, err)

	// Initialize service
	snapService := service.NewSnapService(db, provider, dataDir)

	// Initialize API handler
	handler := api.NewHandler(snapService)

	// Setup Gin router
	gin.SetMode(gin.TestMode)
	r := gin.New()
	handler.SetupRoutes(r)

	// Test 1: Import seed snap
	t.Run("ImportSeedSnap", func(t *testing.T) {
		importReq := service.ImportRequest{
			ID:          "ubuntu-base",
			Envs:        map[string]string{"TEST_ENV": "test_value"},
			DockerImage: "ubuntu:20.04",
		}

		body, _ := json.Marshal(importReq)
		req := httptest.NewRequest("POST", "/api/v1/seed_snap/import", bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()

		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		// Verify snap was created in database
		var snap model.Snap
		err := db.Where("id = ?", "ubuntu-base").First(&snap).Error
		assert.NoError(t, err)
		assert.Equal(t, "ubuntu-base", snap.ID)
		assert.Equal(t, "ubuntu-base", snap.BaseSnapID)
		assert.Equal(t, "FINISHED", snap.Status)
	})

	// Test 2: Execute command on snap
	t.Run("ExecSnap", func(t *testing.T) {
		execReq := service.ExecRequest{
			Command:     []string{"echo", "Hello World"},
			WorkingDir:  "/tmp",
			NewSnapName: "hello-world-snap",
			Envs:        map[string]string{"EXEC_ENV": "exec_value"},
		}

		body, _ := json.Marshal(execReq)
		req := httptest.NewRequest("POST", "/api/v1/forest/test-forest/snap/ubuntu-base/exec", bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()

		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var resp service.ExecResponse
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		assert.NoError(t, err)
		assert.Contains(t, resp.NewSnapID, "ubuntu-base/")

		// Verify new snap was created
		var newSnap model.Snap
		err = db.Where("id = ?", resp.NewSnapID).First(&newSnap).Error
		assert.NoError(t, err)
		assert.Equal(t, "test-forest", newSnap.ForestID)
		assert.Equal(t, "hello-world-snap", newSnap.Name)
		assert.Equal(t, "ubuntu-base", newSnap.SourceSnapID)
		assert.Equal(t, "ubuntu-base", newSnap.BaseSnapID)
		assert.Equal(t, "FINISHED", newSnap.Status)
	})

	// Test 3: Get snap info
	t.Run("GetSnapInfo", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/api/v1/forest/test-forest/snap/ubuntu-base", nil)
		w := httptest.NewRecorder()

		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var info service.SnapInfo
		err := json.Unmarshal(w.Body.Bytes(), &info)
		assert.NoError(t, err)
		assert.Equal(t, "ubuntu-base", info.Name)
		assert.Equal(t, "ubuntu-base", info.BaseSnapID)
		assert.False(t, info.IsRunning)
	})

	// Test 4: Get snap output
	t.Run("GetSnapOutput", func(t *testing.T) {
		// First, find the executed snap
		var execSnap model.Snap
		err := db.Where("forest_id = ? AND source_snap_id = ?", "test-forest", "ubuntu-base").First(&execSnap).Error
		require.NoError(t, err)

		req := httptest.NewRequest("GET", fmt.Sprintf("/api/v1/forest/test-forest/snap/%s/artifacts/output", execSnap.ID), nil)
		w := httptest.NewRecorder()

		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		output, _ := io.ReadAll(w.Body)
		assert.Contains(t, string(output), "Hello World")
	})

	// Test 5: Get snap tree
	t.Run("GetSnapTree", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/api/v1/forest/test-forest/snap/ubuntu-base/tree?maxDepth=2", nil)
		w := httptest.NewRecorder()

		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var tree service.TreeNode
		err := json.Unmarshal(w.Body.Bytes(), &tree)
		assert.NoError(t, err)
		assert.Equal(t, "ubuntu-base", tree.SnapID)
		assert.Len(t, tree.Children, 1)
		assert.Equal(t, "hello-world-snap", tree.Children[0].Name)
	})

	// Test 6: Get snap leaves
	t.Run("GetSnapLeaves", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/api/v1/forest/test-forest/snap/ubuntu-base/leaves", nil)
		w := httptest.NewRecorder()

		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var result map[string][]string
		err := json.Unmarshal(w.Body.Bytes(), &result)
		assert.NoError(t, err)
		assert.Len(t, result["leaves"], 1)
	})

	// Test 7: Burn forest
	t.Run("BurnForest", func(t *testing.T) {
		req := httptest.NewRequest("POST", "/api/v1/forest/test-forest/burn", nil)
		w := httptest.NewRecorder()

		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		// Verify snaps in forest were deleted
		var count int64
		db.Model(&model.Snap{}).Where("forest_id = ?", "test-forest").Count(&count)
		assert.Equal(t, int64(0), count)

		// Verify seed snap still exists
		var seedSnap model.Snap
		err := db.Where("id = ?", "ubuntu-base").First(&seedSnap).Error
		assert.NoError(t, err)
	})
}
