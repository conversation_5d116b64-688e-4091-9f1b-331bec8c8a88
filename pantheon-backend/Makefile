.PHONY: build test test-e2e clean run

# Build the application
build:
	go build -o bin/pantheon-backend .

# Run unit tests
test:
	go test -v ./...

# Run end-to-end tests
test-e2e:
	go test -v ./test/...

# Clean build artifacts
clean:
	rm -rf bin/
	rm -rf data/

# Run the application locally
run:
	go run .

# Install dependencies
deps:
	go mod download
	go mod tidy

# Format code
fmt:
	go fmt ./...

# Lint code
lint:
	golangci-lint run

# Build Docker image
docker-build:
	docker build -t pantheon-backend .

# Run with Docker Compose (if you have docker-compose.yml)
docker-up:
	docker-compose up -d

docker-down:
	docker-compose down
