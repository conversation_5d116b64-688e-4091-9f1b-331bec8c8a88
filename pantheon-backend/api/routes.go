package api

import (
	"io"
	"net/http"
	"pantheon-backend/service"
	"strconv"

	"github.com/gin-gonic/gin"
)

type Handler struct {
	snapService *service.SnapService
}

func NewHandler(snapService *service.SnapService) *Handler {
	return &Handler{
		snapService: snapService,
	}
}

func (h *Handler) SetupRoutes(r *gin.Engine) {
	api := r.Group("/api/v1")

	// Seed snap routes
	api.POST("/seed_snap/import", h.ImportSeedSnap)

	// Forest and snap routes
	api.POST("/forest/:forestID/snap/:snapID/exec", h.ExecSnap)
	api.GET("/forest/:forestID/snap/:snapID", h.GetSnap)
	api.GET("/forest/:forestID/snap/:snapID/artifacts/output", h.GetSnapOutput)
	api.GET("/forest/:forestID/snap/:snapID/tree", h.GetSnapTree)
	api.GET("/forest/:forestID/snap/:snapID/leaves", h.GetSnapLeaves)
	api.POST("/forest/:forestID/burn", h.BurnForest)

	// Alternative routes for snap IDs with slashes - use query parameters
	api.GET("/forest/:forestID/output", h.GetSnapOutputByQuery)
	api.GET("/forest/:forestID/tree", h.GetSnapTreeByQuery)
	api.GET("/forest/:forestID/leaves", h.GetSnapLeavesByQuery)
}

func (h *Handler) ImportSeedSnap(c *gin.Context) {
	var req service.ImportRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.snapService.ImportSeedSnap(&req); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "seed snap imported successfully"})
}

func (h *Handler) ExecSnap(c *gin.Context) {
	forestID := c.Param("forestID")
	snapID := c.Param("snapID")

	var req service.ExecRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.snapService.ExecSnap(forestID, snapID, &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *Handler) GetSnap(c *gin.Context) {
	snapID := c.Param("snapID")

	info, err := h.snapService.GetSnapInfo(snapID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, info)
}

func (h *Handler) GetSnapOutput(c *gin.Context) {
	forestID := c.Param("forestID")
	snapID := c.Param("snapID")

	output, err := h.snapService.GetSnapOutput(forestID, snapID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}
	defer output.Close()

	// Read all output and return as string
	content, err := io.ReadAll(output)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to read output"})
		return
	}

	c.Header("Content-Type", "text/plain")
	c.String(http.StatusOK, string(content))
}

func (h *Handler) GetSnapTree(c *gin.Context) {
	snapID := c.Param("snapID")
	maxDepthStr := c.DefaultQuery("maxDepth", "0")

	maxDepth, err := strconv.Atoi(maxDepthStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid maxDepth parameter"})
		return
	}

	tree, err := h.snapService.GetSnapTree(snapID, maxDepth)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, tree)
}

func (h *Handler) GetSnapLeaves(c *gin.Context) {
	snapID := c.Param("snapID")

	leaves, err := h.snapService.GetSnapLeaves(snapID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"leaves": leaves})
}

func (h *Handler) BurnForest(c *gin.Context) {
	forestID := c.Param("forestID")

	if err := h.snapService.BurnForest(forestID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "forest burned successfully"})
}

// Alternative handlers for snap IDs with slashes
func (h *Handler) GetSnapOutputByQuery(c *gin.Context) {
	forestID := c.Param("forestID")
	snapID := c.Query("snapID")

	if snapID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "snapID query parameter is required"})
		return
	}

	output, err := h.snapService.GetSnapOutput(forestID, snapID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}
	defer output.Close()

	// Read all output and return as string
	content, err := io.ReadAll(output)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to read output"})
		return
	}

	c.Header("Content-Type", "text/plain")
	c.String(http.StatusOK, string(content))
}

func (h *Handler) GetSnapTreeByQuery(c *gin.Context) {
	snapID := c.Query("snapID")
	maxDepthStr := c.DefaultQuery("maxDepth", "0")

	if snapID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "snapID query parameter is required"})
		return
	}

	maxDepth, err := strconv.Atoi(maxDepthStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid maxDepth parameter"})
		return
	}

	tree, err := h.snapService.GetSnapTree(snapID, maxDepth)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, tree)
}

func (h *Handler) GetSnapLeavesByQuery(c *gin.Context) {
	snapID := c.Query("snapID")

	if snapID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "snapID query parameter is required"})
		return
	}

	leaves, err := h.snapService.GetSnapLeaves(snapID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"leaves": leaves})
}
