package local

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"os"
	"pantheon-backend/provider"
	"strings"
	"time"

	"github.com/docker/docker/api/types"
	"github.com/docker/docker/api/types/container"
	"github.com/docker/docker/client"
)

type LocalProvider struct {
	dockerClient *client.Client
}

func NewLocalProvider() (*LocalProvider, error) {
	// Try different Docker socket paths for macOS compatibility
	var cli *client.Client
	var err error

	// Possible Docker socket paths
	socketPaths := []string{
		"", // Use environment default
		"unix://" + os.Getenv("HOME") + "/.docker/run/docker.sock",
		"unix:///var/run/docker.sock",
		"unix://" + os.Getenv("HOME") + "/Library/Containers/com.docker.docker/Data/docker-cli.sock",
		"unix://" + os.Getenv("HOME") + "/.orbstack/run/docker.sock",
	}

	for _, socketPath := range socketPaths {
		if socketPath == "" {
			// Try default from environment
			cli, err = client.NewClientWithOpts(client.FromEnv, client.WithAPIVersionNegotiation())
		} else {
			cli, err = client.NewClientWithOpts(
				client.WithHost(socketPath),
				client.WithAPIVersionNegotiation(),
			)
		}

		if err == nil {
			// Test the connection
			ctx := context.Background()
			_, err = cli.Ping(ctx)
			if err == nil {
				break
			}
		}
	}

	if err != nil {
		return nil, fmt.Errorf("failed to create docker client: %w", err)
	}

	return &LocalProvider{
		dockerClient: cli,
	}, nil
}

func (p *LocalProvider) Import(baseImage string) (string, error) {
	ctx := context.Background()

	// Pull the image
	reader, err := p.dockerClient.ImagePull(ctx, baseImage, types.ImagePullOptions{})
	if err != nil {
		return "", fmt.Errorf("failed to pull image %s: %w", baseImage, err)
	}
	defer reader.Close()

	// Wait for pull to complete
	_, err = io.Copy(io.Discard, reader)
	if err != nil {
		return "", fmt.Errorf("failed to wait for image pull: %w", err)
	}

	// Inspect the image to get its ID
	inspect, _, err := p.dockerClient.ImageInspectWithRaw(ctx, baseImage)
	if err != nil {
		return "", fmt.Errorf("failed to inspect image %s: %w", baseImage, err)
	}

	return inspect.ID, nil
}

func (p *LocalProvider) Exec(sourceProviderID string, command []string, workDir string, envs map[string]string) (*provider.ProviderOutput, error) {
	ctx := context.Background()

	// Create env file
	envFile, err := p.createEnvFile(envs)
	if err != nil {
		return nil, fmt.Errorf("failed to create env file: %w", err)
	}
	defer os.Remove(envFile)

	// Start container with sleep infinity
	containerConfig := &container.Config{
		Image:      sourceProviderID,
		Cmd:        []string{"sleep", "infinity"},
		WorkingDir: workDir,
		Env:        p.mapToEnvSlice(envs),
	}

	resp, err := p.dockerClient.ContainerCreate(ctx, containerConfig, nil, nil, nil, "")
	if err != nil {
		return nil, fmt.Errorf("failed to create container: %w", err)
	}

	containerID := resp.ID

	// Start the container
	if err := p.dockerClient.ContainerStart(ctx, containerID, types.ContainerStartOptions{}); err != nil {
		p.dockerClient.ContainerRemove(ctx, containerID, types.ContainerRemoveOptions{Force: true})
		return nil, fmt.Errorf("failed to start container: %w", err)
	}

	// Execute the command and capture output
	cmdStr := strings.Join(command, " ")
	execConfig := types.ExecConfig{
		Cmd:          []string{"/bin/sh", "-c", cmdStr},
		AttachStdout: true,
		AttachStderr: true,
	}

	execResp, err := p.dockerClient.ContainerExecCreate(ctx, containerID, execConfig)
	if err != nil {
		p.dockerClient.ContainerRemove(ctx, containerID, types.ContainerRemoveOptions{Force: true})
		return nil, fmt.Errorf("failed to create exec: %w", err)
	}

	hijackedResp, err := p.dockerClient.ContainerExecAttach(ctx, execResp.ID, types.ExecStartCheck{})
	if err != nil {
		p.dockerClient.ContainerRemove(ctx, containerID, types.ContainerRemoveOptions{Force: true})
		return nil, fmt.Errorf("failed to attach to exec: %w", err)
	}

	// Read all output
	var outputBuffer bytes.Buffer
	_, err = io.Copy(&outputBuffer, hijackedResp.Reader)
	hijackedResp.Close()

	// Wait for exec to finish
	for {
		inspect, err := p.dockerClient.ContainerExecInspect(ctx, execResp.ID)
		if err != nil {
			break
		}
		if !inspect.Running {
			break
		}
		time.Sleep(100 * time.Millisecond)
	}

	// Commit the container to create new image
	commitResp, err := p.dockerClient.ContainerCommit(ctx, containerID, types.ContainerCommitOptions{})
	if err != nil {
		p.dockerClient.ContainerRemove(ctx, containerID, types.ContainerRemoveOptions{Force: true})
		return nil, fmt.Errorf("failed to commit container: %w", err)
	}

	// Clean up container
	p.dockerClient.ContainerRemove(ctx, containerID, types.ContainerRemoveOptions{Force: true})

	// Create readers from the captured output
	outputStr := outputBuffer.String()
	stdout := io.NopCloser(strings.NewReader(outputStr))
	stderr := io.NopCloser(strings.NewReader(""))

	return &provider.ProviderOutput{
		NewProviderID: commitResp.ID,
		Stdout:        stdout,
		Stderr:        stderr,
	}, nil
}

func (p *LocalProvider) Cleanup(providerID string) error {
	ctx := context.Background()
	_, err := p.dockerClient.ImageRemove(ctx, providerID, types.ImageRemoveOptions{Force: true})
	return err
}

func (p *LocalProvider) createEnvFile(envs map[string]string) (string, error) {
	if len(envs) == 0 {
		return "", nil
	}

	file, err := os.CreateTemp("", "pantheon-env-*.txt")
	if err != nil {
		return "", err
	}
	defer file.Close()

	for key, value := range envs {
		_, err := fmt.Fprintf(file, "%s=%s\n", key, value)
		if err != nil {
			os.Remove(file.Name())
			return "", err
		}
	}

	return file.Name(), nil
}

func (p *LocalProvider) mapToEnvSlice(envs map[string]string) []string {
	var result []string
	for key, value := range envs {
		result = append(result, fmt.Sprintf("%s=%s", key, value))
	}
	return result
}
