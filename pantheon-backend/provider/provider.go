package provider

import "io"

// ProviderOutput is the result of an execution.
type ProviderOutput struct {
	NewProviderID string // The ID of the new snapshot in the provider's context (e.g., Docker image hash).
	Stdout        io.ReadCloser
	Stderr        io.ReadCloser
}

// Provider is the interface for snapshot and execution engines.
type Provider interface {
	// Import creates a base snapshot from a given image and returns its provider-specific ID.
	// `envs` are metadata to be associated, not directly baked into the image unless absolutely necessary.
	Import(baseImage string) (string, error)

	// Exec runs a command on a source snapshot and creates a new one.
	Exec(sourceProviderID string, command []string, workDir string, envs map[string]string) (*ProviderOutput, error)

	// Cleanup removes a snapshot managed by the provider.
	Cleanup(providerID string) error
}
