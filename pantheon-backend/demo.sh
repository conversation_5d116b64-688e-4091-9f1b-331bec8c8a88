#!/bin/bash

# Pantheon Backend Demo Script
# This script demonstrates the basic functionality of Pantheon

set -e

echo "🚀 Pantheon Backend Demo"
echo "========================"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Start MySQL container for demo
echo "📦 Starting MySQL container..."
docker run -d --name pantheon-mysql \
    -e MYSQL_ROOT_PASSWORD=demo123 \
    -e MYSQL_DATABASE=pantheon \
    -p 3306:3306 \
    mysql:8.0 > /dev/null

echo "⏳ Waiting for MySQL to be ready..."
sleep 15

# Set environment variables
export DATABASE_URL="root:demo123@tcp(localhost:3306)/pantheon?charset=utf8mb4&parseTime=True&loc=Local"
export DATA_DIR="./demo-data"
export PORT="8080"

# Create data directory
mkdir -p demo-data

echo "🏗️  Building Pantheon..."
go build -o bin/pantheon-backend .

echo "🚀 Starting Pantheon server..."
./bin/pantheon-backend &
SERVER_PID=$!

# Wait for server to start
sleep 3

echo "📋 Running API demonstrations..."

# 1. Import a seed snap
echo "1️⃣  Importing seed snap (Ubuntu 20.04)..."
curl -s -X POST http://localhost:8080/api/v1/seed_snap/import \
    -H "Content-Type: application/json" \
    -d '{
        "id": "ubuntu-demo",
        "envs": {"DEMO": "true"},
        "dockerImage": "ubuntu:20.04"
    }' | jq .

# 2. Execute a command
echo "2️⃣  Executing command: echo 'Hello Pantheon'..."
EXEC_RESPONSE=$(curl -s -X POST http://localhost:8080/api/v1/forest/demo-forest/snap/ubuntu-demo/exec \
    -H "Content-Type: application/json" \
    -d '{
        "command": ["echo", "Hello Pantheon"],
        "workingDir": "/tmp",
        "newSnapName": "hello-snap"
    }')

echo $EXEC_RESPONSE | jq .
NEW_SNAP_ID=$(echo $EXEC_RESPONSE | jq -r .newSnapID)

# 3. Get snap info
echo "3️⃣  Getting snap information..."
curl -s "http://localhost:8080/api/v1/forest/demo-forest/snap/$NEW_SNAP_ID" | jq .

# 4. Get command output
echo "4️⃣  Getting command output..."
ENCODED_SNAP_ID=$(python3 -c "import urllib.parse; print(urllib.parse.quote('$NEW_SNAP_ID'))")
curl -s "http://localhost:8080/api/v1/forest/demo-forest/output?snapID=$ENCODED_SNAP_ID"

# 5. Execute another command on the new snap
echo "5️⃣  Executing another command: ls -la /tmp..."
EXEC_RESPONSE2=$(curl -s -X POST http://localhost:8080/api/v1/forest/demo-forest/snap/$NEW_SNAP_ID/exec \
    -H "Content-Type: application/json" \
    -d '{
        "command": ["ls", "-la", "/tmp"],
        "workingDir": "/tmp",
        "newSnapName": "ls-snap"
    }')

echo $EXEC_RESPONSE2 | jq .
NEW_SNAP_ID2=$(echo $EXEC_RESPONSE2 | jq -r .newSnapID)

# 6. Get snap tree
echo "6️⃣  Getting snap tree..."
ENCODED_ROOT_ID=$(python3 -c "import urllib.parse; print(urllib.parse.quote('ubuntu-demo'))")
curl -s "http://localhost:8080/api/v1/forest/demo-forest/tree?snapID=$ENCODED_ROOT_ID&maxDepth=3" | jq .

# 7. Get snap leaves
echo "7️⃣  Getting snap leaves..."
curl -s "http://localhost:8080/api/v1/forest/demo-forest/leaves?snapID=$ENCODED_ROOT_ID" | jq .

# 8. Burn the forest
echo "8️⃣  Burning the forest..."
curl -s -X POST http://localhost:8080/api/v1/forest/demo-forest/burn | jq .

echo "✅ Demo completed successfully!"

# Cleanup
echo "🧹 Cleaning up..."
kill $SERVER_PID
docker stop pantheon-mysql > /dev/null
docker rm pantheon-mysql > /dev/null
rm -rf demo-data

echo "🎉 Demo finished!"
