version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: pantheon-mysql
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: pantheon
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  pantheon:
    build: .
    container_name: pantheon-backend
    environment:
      DATABASE_URL: "root:password@tcp(mysql:3306)/pantheon?charset=utf8mb4&parseTime=True&loc=Local"
      DATA_DIR: "/app/data"
      PORT: "8080"
    ports:
      - "8080:8080"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - pantheon_data:/app/data
    depends_on:
      mysql:
        condition: service_healthy

volumes:
  mysql_data:
  pantheon_data:
