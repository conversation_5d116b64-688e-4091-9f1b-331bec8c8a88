# Pantheon Backend

Pantheon is a Golang HTTP service that provides fast snapshot capabilities based on Dock<PERSON>'s LayeredFS. It allows you to execute commands in isolated environments and create snapshots for later use.

## Features

- **Snapshot Management**: Create and manage snapshots based on Docker images
- **Command Execution**: Execute commands in isolated environments with snapshot creation
- **Tree Structure**: Organize snapshots in a tree-like structure with ancestry tracking
- **Forest Management**: Group related snapshots in forests for easy management
- **RESTful API**: Complete REST API for all operations

## Architecture

The application follows a clean 4-layer architecture:

1. **API Layer** (`api/`): HTTP request handling with Gin framework
2. **Service Layer** (`service/`): Business logic and orchestration
3. **Provider Interface** (`provider/`): Abstraction for snapshot engines
4. **Provider Implementation** (`provider/local/`): Docker-based local implementation

## Prerequisites

- Go 1.21+
- Docker
- MySQL 8.0+

## Quick Start

### Option 1: Using Docker Compose (Recommended)

```bash
# Clone and start everything
git clone <repository>
cd pantheon-backend
docker-compose up -d

# The API will be available at http://localhost:8080
```

### Option 2: Manual Setup

1. **Clone and build**:

   ```bash
   git clone <repository>
   cd pantheon-backend
   make deps
   make build
   ```

2. **Set up MySQL**:

   ```bash
   # Using Docker
   docker run -d --name mysql-pantheon \
     -e MYSQL_ROOT_PASSWORD=password \
     -e MYSQL_DATABASE=pantheon \
     -p 3306:3306 mysql:8.0
   ```

3. **Run the application**:
   ```bash
   export DATABASE_URL="root:password@tcp(localhost:3306)/pantheon?charset=utf8mb4&parseTime=True&loc=Local"
   export DATA_DIR="./data"
   make run
   ```

### Option 3: Run Demo

```bash
# Run the interactive demo (requires jq and python3)
./demo.sh
```

## API Endpoints

### Seed Snap Management

- `POST /api/v1/seed_snap/import` - Import a Docker image as a seed snap

### Snap Operations

- `POST /api/v1/forest/{forestID}/snap/{snapID}/exec` - Execute command on a snap
- `GET /api/v1/forest/{forestID}/snap/{snapID}` - Get snap information
- `GET /api/v1/forest/{forestID}/snap/{snapID}/artifacts/output` - Get command output
- `GET /api/v1/forest/{forestID}/snap/{snapID}/tree` - Get snap tree structure
- `GET /api/v1/forest/{forestID}/snap/{snapID}/leaves` - Get leaf snaps

### Forest Management

- `POST /api/v1/forest/{forestID}/burn` - Destroy all snaps in a forest

### Alternative APIs for Snap IDs with Slashes

Since snap IDs can contain slashes (e.g., `base-snap/child-snap`), which conflict with URL paths, alternative query-based APIs are provided:

- `GET /api/v1/forest/{forestID}/output?snapID={snapID}` - Get snap output using query parameter
- `GET /api/v1/forest/{forestID}/tree?snapID={snapID}&maxDepth={depth}` - Get snap tree using query parameter
- `GET /api/v1/forest/{forestID}/leaves?snapID={snapID}` - Get snap leaves using query parameter

## Example Usage

1. **Import a seed snap**:

   ```bash
   curl -X POST http://localhost:8080/api/v1/seed_snap/import \
     -H "Content-Type: application/json" \
     -d '{
       "id": "ubuntu-base",
       "envs": {"ENV_VAR": "value"},
       "dockerImage": "ubuntu:20.04"
     }'
   ```

2. **Execute a command**:

   ```bash
   curl -X POST http://localhost:8080/api/v1/forest/my-forest/snap/ubuntu-base/exec \
     -H "Content-Type: application/json" \
     -d '{
       "command": ["echo", "Hello World"],
       "workingDir": "/tmp",
       "newSnapName": "hello-snap"
     }'
   ```

3. **Get command output**:
   ```bash
   # Use query parameter API for snap IDs with slashes
   curl "http://localhost:8080/api/v1/forest/my-forest/output?snapID=ubuntu-base%2Fa1b2c3d4"
   ```

## Testing

Run the complete end-to-end test suite:

```bash
make test-e2e
```

The tests use testcontainers to spin up a MySQL instance and test the complete workflow.

## Configuration

Environment variables:

- `DATABASE_URL`: MySQL connection string (default: `root:password@tcp(localhost:3306)/pantheon?charset=utf8mb4&parseTime=True&loc=Local`)
- `DATA_DIR`: Directory for storing output artifacts (default: `./data`)
- `PORT`: HTTP server port (default: `8080`)

## Development

- `make build` - Build the application
- `make test` - Run unit tests
- `make test-e2e` - Run end-to-end tests
- `make fmt` - Format code
- `make clean` - Clean build artifacts

## License

MIT License
