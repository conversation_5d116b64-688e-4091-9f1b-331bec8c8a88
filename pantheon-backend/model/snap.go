package model

import (
	"gorm.io/datatypes"
	"time"
)

type Snap struct {
	ID           string `gorm:"primaryKey;type:varchar(255)"` // e.g., "my-base" or "my-base/a1b2c3d4"
	ForestID     string `gorm:"index;type:varchar(128)"`
	Name         string `gorm:"type:varchar(255)"` // User-defined newSnapName

	ProviderID string `gorm:"uniqueIndex;type:varchar(255)"` // The real ID from provider, e.g., Docker image hash sha256:...

	SourceSnapID string `gorm:"type:varchar(255)"` // The immediate parent's ID
	BaseSnapID   string `gorm:"index;type:varchar(128)"`   // The seed snap ID, e.g., "my-base"

	// JSON array of ancestor IDs. Solves the tree query problem efficiently.
	// e.g., ["base-id", "base-id/snap1", "base-id/snap2"]
	AncestryPath datatypes.JSON `gorm:"type:json"`

	Command string
	WorkDir string
	Envs    datatypes.JSON `gorm:"type:json"` // Store the envs used for this snap execution.

	Status     string `gorm:"type:varchar(20)"` // PENDING, RUNNING, FINISHED, FAILED
	StartedAt  *time.Time
	FinishedAt *time.Time

	// Path to the stored output artifacts, relative to some base path.
	// e.g., "{ForestID}/{SnapID}/output.log"
	OutputPath string
}
