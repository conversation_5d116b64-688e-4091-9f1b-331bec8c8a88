package main

import (
	"log"
	"os"
	"pantheon-backend/api"
	"pantheon-backend/model"
	"pantheon-backend/provider/local"
	"pantheon-backend/service"

	"github.com/gin-gonic/gin"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

func main() {
	// Database connection
	dsn := os.Getenv("DATABASE_URL")
	if dsn == "" {
		dsn = "root:password@tcp(localhost:3306)/pantheon?charset=utf8mb4&parseTime=True&loc=Local"
	}

	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// Auto migrate
	if err := db.AutoMigrate(&model.Snap{}); err != nil {
		log.Fatal("Failed to migrate database:", err)
	}

	// Data directory
	dataDir := os.Getenv("DATA_DIR")
	if dataDir == "" {
		dataDir = "./data"
	}

	// Create data directory
	if err := os.MkdirAll(dataDir, 0755); err != nil {
		log.Fatal("Failed to create data directory:", err)
	}

	// Initialize provider
	provider, err := local.NewLocalProvider()
	if err != nil {
		log.Fatal("Failed to initialize provider:", err)
	}

	// Initialize service
	snapService := service.NewSnapService(db, provider, dataDir)

	// Initialize API handler
	handler := api.NewHandler(snapService)

	// Setup Gin
	r := gin.Default()
	handler.SetupRoutes(r)

	// Start server
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}

	log.Printf("Starting server on port %s", port)
	if err := r.Run(":" + port); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
